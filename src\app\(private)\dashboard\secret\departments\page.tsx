"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/Card";
import { toast } from "react-toastify";
import {
  Home,
  Plus,
  Edit3,
  Trash2,
  Users,
  Save,
  X
} from "react-feather";
import departmentApiRequest, { Department } from "@/apiRequests/department";

export default function DepartmentsManagement() {
  const [sessionToken, setSessionToken] = useState<string>("");
  const [departments, setDepartments] = useState<Department[]>([]);
  const [loading, setLoading] = useState(true);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);
  const [newDepartmentName, setNewDepartmentName] = useState("");
  const [editDepartmentName, setEditDepartmentName] = useState("");
  const [tokenLoaded, setTokenLoaded] = useState(false);

  // Get session token on client side only
  useEffect(() => {
    const token = localStorage.getItem("sessionToken") || "";
    setSessionToken(token);
    setTokenLoaded(true);
  }, []);

  // Fetch departments
  const fetchDepartments = async () => {
    if (!sessionToken) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const result = await departmentApiRequest.getDepartments(sessionToken);
      if (result.payload.success) {
        setDepartments(result.payload.data || []);
      } else {
        console.error("API Error:", result.payload.message);
        toast.error(result.payload.message || "Lỗi khi tải danh sách phòng ban");
      }
    } catch (error: any) {
      console.error("Error fetching departments:", error);
      if (error?.status === 401) {
        toast.error("Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.");
      } else {
        toast.error("Lỗi khi tải danh sách phòng ban");
      }
    } finally {
      setLoading(false);
    }
  };

  // Create department
  const handleCreateDepartment = async () => {
    if (!sessionToken || !newDepartmentName.trim()) return;

    try {
      const result = await departmentApiRequest.createDepartment(sessionToken, newDepartmentName.trim());
      if (result.payload.success) {
        toast.success(result.payload.message);
        setNewDepartmentName("");
        setShowAddForm(false);
        fetchDepartments();
      } else {
        toast.error(result.payload.message);
      }
    } catch (error) {
      console.error("Error creating department:", error);
      toast.error("Lỗi khi tạo phòng ban");
    }
  };

  // Update department
  const handleUpdateDepartment = async (id: string) => {
    if (!sessionToken || !editDepartmentName.trim()) return;

    try {
      const result = await departmentApiRequest.updateDepartment(sessionToken, id, editDepartmentName.trim());
      if (result.payload.success) {
        toast.success(result.payload.message);
        setEditingId(null);
        setEditDepartmentName("");
        fetchDepartments();
      } else {
        toast.error(result.payload.message);
      }
    } catch (error) {
      console.error("Error updating department:", error);
      toast.error("Lỗi khi cập nhật phòng ban");
    }
  };

  // Delete department
  const handleDeleteDepartment = async (id: string, name: string) => {
    if (!sessionToken) return;

    if (!confirm(`Bạn có chắc chắn muốn xóa phòng ban "${name}"?`)) return;

    try {
      const result = await departmentApiRequest.deleteDepartment(sessionToken, id);
      if (result.payload.success) {
        toast.success(result.payload.message);
        fetchDepartments();
      } else {
        toast.error(result.payload.message);
      }
    } catch (error) {
      console.error("Error deleting department:", error);
      toast.error("Lỗi khi xóa phòng ban");
    }
  };

  // Start editing
  const startEdit = (department: Department) => {
    setEditingId(department.id);
    setEditDepartmentName(department.name);
  };

  // Cancel editing
  const cancelEdit = () => {
    setEditingId(null);
    setEditDepartmentName("");
  };

  useEffect(() => {
    // Only run after token is loaded from localStorage
    if (!tokenLoaded) return;

    console.log("Department page - sessionToken:", sessionToken ? "exists" : "missing");

    // Set timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      if (loading) {
        setLoading(false);
        toast.error("Timeout: Không thể tải danh sách phòng ban");
      }
    }, 10000); // 10 seconds timeout

    fetchDepartments();

    return () => clearTimeout(timeout);
  }, [sessionToken, tokenLoaded]);

  if (loading || !tokenLoaded) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">
            {!tokenLoaded ? "Đang kiểm tra phiên đăng nhập..." : "Đang tải danh sách phòng ban..."}
          </p>
          <p className="mt-2 text-sm text-gray-500">
            Token: {tokenLoaded ? (sessionToken ? "✓" : "✗") : "Đang tải..."}
          </p>
        </div>
      </div>
    );
  }

  if (!sessionToken) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-red-600">Không có quyền truy cập. Vui lòng đăng nhập lại.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Home className="h-8 w-8 text-blue-600" />
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Quản lý Phòng ban</h1>
            <p className="text-gray-600">Quản lý danh sách các phòng ban trong tổ chức</p>
          </div>
        </div>
        <button
          onClick={() => setShowAddForm(true)}
          className="flex items-center space-x-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors"
        >
          <Plus size={20} />
          <span>Thêm phòng ban</span>
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <Card className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Tổng phòng ban</p>
              <p className="text-2xl font-bold text-gray-900">{departments.length}</p>
            </div>
            <Home className="h-8 w-8 text-blue-600" />
          </div>
        </Card>
      </div>

      {/* Add Form */}
      {showAddForm && (
        <Card className="p-6 mb-6">
          <h3 className="text-lg font-semibold mb-4">Thêm phòng ban mới</h3>
          <div className="flex items-center space-x-4">
            <input
              type="text"
              value={newDepartmentName}
              onChange={(e) => setNewDepartmentName(e.target.value)}
              placeholder="Tên phòng ban"
              className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
              onKeyPress={(e) => e.key === 'Enter' && handleCreateDepartment()}
            />
            <button
              onClick={handleCreateDepartment}
              disabled={!newDepartmentName.trim()}
              className="flex items-center space-x-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <Save size={16} />
              <span>Lưu</span>
            </button>
            <button
              onClick={() => {
                setShowAddForm(false);
                setNewDepartmentName("");
              }}
              className="flex items-center space-x-2 bg-gray-500 text-white px-4 py-2 rounded-lg hover:bg-gray-600 transition-colors"
            >
              <X size={16} />
              <span>Hủy</span>
            </button>
          </div>
        </Card>
      )}

      {/* Departments List */}
      <Card className="overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-semibold">Danh sách phòng ban</h3>
        </div>
        
        {departments.length === 0 ? (
          <div className="p-8 text-center">
            <Home className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500">Chưa có phòng ban nào</p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {departments.map((department) => (
              <div key={department.id} className="p-6 flex items-center justify-between hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Home size={20} className="text-blue-600" />
                  </div>
                  <div>
                    {editingId === department.id ? (
                      <input
                        type="text"
                        value={editDepartmentName}
                        onChange={(e) => setEditDepartmentName(e.target.value)}
                        className="px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
                        onKeyPress={(e) => e.key === 'Enter' && handleUpdateDepartment(department.id)}
                        autoFocus
                      />
                    ) : (
                      <>
                        <h4 className="font-medium text-gray-900">{department.name}</h4>
                        <p className="text-sm text-gray-500">ID: {department.id}</p>
                      </>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  {editingId === department.id ? (
                    <>
                      <button
                        onClick={() => handleUpdateDepartment(department.id)}
                        className="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors"
                        title="Lưu"
                      >
                        <Save size={16} />
                      </button>
                      <button
                        onClick={cancelEdit}
                        className="p-2 text-gray-600 hover:bg-gray-100 rounded-lg transition-colors"
                        title="Hủy"
                      >
                        <X size={16} />
                      </button>
                    </>
                  ) : (
                    <>
                      <button
                        onClick={() => startEdit(department)}
                        className="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors"
                        title="Chỉnh sửa"
                      >
                        <Edit3 size={16} />
                      </button>
                      <button
                        onClick={() => handleDeleteDepartment(department.id, department.name)}
                        className="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors"
                        title="Xóa"
                      >
                        <Trash2 size={16} />
                      </button>
                    </>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}
      </Card>
    </div>
  );
}
