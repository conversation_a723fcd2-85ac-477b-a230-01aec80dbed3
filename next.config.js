/** @type {import('next').NextConfig} */
module.exports = {
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "tand.hochiminhcity.gov.vn",
      },
      {
        protocol: "http",
        hostname: "tand.hochiminhcity.gov.vn",
      },
      {
        protocol: "https",
        hostname: "localhost",
        port: "8000",
      },
      {
        protocol: "http",
        hostname: "localhost",
        port: "8000",
      },
    ],
    formats: ["image/avif", "image/webp"],
  },
  env: {
    NEXT_PUBLIC_SITE_NAME: "TANDHCM",
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Disable dev indicators
  devIndicators: false,
  // Additional security configurations
  poweredByHeader: false,
  compress: true,
};